# Technical Design: umee MVP

## 1. Introduction & Vision

This document outlines the technical architecture for the **umee** Minimum Viable Product (MVP). The project's goal is to create a mobile application for iOS and Android that allows users to create profiles, discover and connect with others through prompts, and communicate via a real-time chat.

The architecture is designed to be scalable, secure, and maintainable, leveraging a hybrid cloud model that combines AWS serverless components for core business logic and a dedicated compute layer for real-time communication.

## 2. Overall System Architecture

The system consists of a Flutter client that interacts with a set of managed AWS services. AWS Amplify serves as the primary SDK for orchestrating these interactions.

```text
+------------------------+
| Flutter Mobile App     |
| (umee)                 |
+-----------+------------+
            |
            | (Auth & API via Amplify SDK)
            |
            v
+-----------+-------------+       +------------------------+
|   AWS AppSync           |------>|       AWS Cognito      |
|   (GraphQL API)         |       |       (Auth)           |
+-----------+-------------+       +------------------------+
            |
            | (Invokes Lambda Resolvers)
            |
            v
+-----------+-------------+
|   AWS Lambda Functions  |
+-----------+-------------+
            |
+-----------+----------------------+---------------------------+
| (CRUD Ops)| (File Ops)           | (Triggers Notifications)  |
v           v                      v                           v
+-----------+  +-----------------+  +-----------------------+  +------------------------+
| DynamoDB  |  |   Amazon S3     |  |      AWS Pinpoint     |  | EC2 (Socket.io Chat)   |
| (Data)    |  |   (Storage)     |  | (Sends to APNS/FCM)   |  | (Via Load Balancer)    |
+-----------+  +-----------------+  +-----------------------+  +------------------------+

    
3. Data Model Definitions (DynamoDB)

Based on the presentation, the core data models are:

    User Table:

        Id (PK, uuidv7)

        firstName, lastName (string)

        profileBio (string)

        inviteCode (string)

        communityID (uuidv7)

        prompts (json): Stores user's question-answer pairs. Example: {"prompt1": "My favorite food is...", "answer1": "Pizza"}.

        device_token (string): For push notifications.

        createdAt, updatedAt (timestamp)

    UserRelationship Table:

        Id (PK, uuidv7)

        sourceUserID (GSI PK)

        targetUserID (GSI PK)

        status (string): Enum of {blocked_by, blocked, friend, none, received, sent}.

        createdAt, updatedAt (timestamp)

    NewsfeedPost Table (Inferred):

        Id (PK)

        communityID (GSI PK)

        authorID (string)

        content (string)

        mediaUrl (string, optional, points to S3)

        createdAt (timestamp, GSI SK for sorting)

4. Feature-Specific Architecture Breakdowns
4.1. Profile Screen

The profile screen displays user information, including their bio and profile picture. It allows for image uploads and updates.
      
Client                      Backend
+-----------------+         +---------------------+
| Profile Manager |<------->| Local Cache         |
+-------+---------+         +---------------------+
        |
(Update Image via GraphQL)
        |
        v
+-----------------+         +---------------------+
| Amplify Client  |------>  | AppSync Resolvers   |
+-----------------+         +----------+----------+
                                       | (HTTPS API Call to S3)
                                       v
                                +---------------------+
                                |      Amazon S3      |
                                +---------------------+
```**Workflow:**
1.  **Initial Display:** The `Profile Manager` on the client first attempts to retrieve and display the user's profile image from a `Local Cache` for fast loading.
2.  **Cache Miss:** If the image isn't cached, it's fetched from S3.
3.  **Uploading/Updating Image:** The Amplify client sends a GraphQL mutation to AppSync. The resolver generates a pre-signed URL for S3, which the client uses to upload the image directly. The client then updates its local cache.

### 4.2. Meet Screen

The "Meet" screen is for user discovery. It displays other users' profiles, including their pictures and prompt responses.

```text
Client                      Backend
+-----------------+         +---------------------+         +---------------------+
| Profile Manager |<------->| Local Cache         |<--+-----|      Amazon S3      |
+-------+---------+         +---------------------+   |     +---------------------+
        |                                             | 
(Get Users via GraphQL)                               | (Get Image URLs)
        |                                             |
        v                                             |
+-----------------+         +---------------------+   |
| Amplify Client  |------>  | AppSync Resolvers   |---+
+-----------------+         +----------+----------+
                                       |
                             (Fetch User Data & Prompts)
                                       |
                                       v
                                +---------------------+
                                |      DynamoDB       |
                                +---------------------+

    

Workflow:

    Data Fetch: The client sends a GraphQL query to AppSync to fetch a list of users.

    Resolver Logic: The AppSync resolver queries the DynamoDB User table to get user data, including the prompts JSON attribute.

    Image Handling: Profile pictures are displayed using the same cache-first logic as the Profile Screen.

4.3. Newsfeed Screen

The newsfeed shows posts from users within the same community.
      
Client                      Backend
+------------------+        +---------------------+
| Newsfeed Manager |<------>| Local Cache         |
+-------+----------+        +---------------------+
        |
(Get Posts via GraphQL)
        |
        v
+------------------+        +---------------------+
| Amplify Client   |------->| AppSync Resolvers   |
+------------------+        +----------+----------+
                                       |
                                (Fetch communityPosts)
                                       |
                                       v
                                +---------------------+
                                |      DynamoDB       |
                                +---------------------+

    

Workflow:

    Data Fetch: The client sends a GraphQL query to AppSync to get the latest communityPosts.

    Resolver Logic: The AppSync resolver queries the DynamoDB NewsfeedPost table using a GSI on communityID to efficiently fetch all relevant posts.

    Media Handling: Any images or videos attached to posts are handled with the same cache-first mechanism.

4.4. Real-Time Chat

The chat feature uses a dedicated, stateful real-time layer for low-latency messaging.
      
Client Device
+------------------------+      +-------------------+
| Amplify App            |----->| Socket.io Client  |
| (Manages Local Cache)  |      +---------+---------+
+------------------------+                |
                                (WSS Connection)
                                          |
                                          v
AWS Infrastructure
+------------------------+      +------------------------+
| Certificate Manager    |----->| App Load Balancer (ALB)|
| (Handles SSL/TLS)      |      +---------+--------------+
+------------------------+                |
                                          v
                           +----------------------------------+
                           | Amazon EC2 (w/ Socket.io Server) |
                           +----------------+-----------------+
                                            | (Logs)
                                            v
                               +------------------------------+
                               |          CloudWatch          |
                               +------------------------------+

    

Workflow:

    Connection: The Socket.io Client connects via a secure WebSocket (WSS) to the Application Load Balancer (ALB).

    EC2 Server: The ALB forwards traffic to the Socket.io Server on EC2, which manages connections and message broadcasting.

    Logging: All server-side events and errors from the EC2 instance are pushed to CloudWatch.

4.5. Updates & Notifications Screen

This feature delivers push notifications to users for events like new messages or friend requests.
      
Client                      Backend
+------------------+
| Amplify Client   |
+--------+---------+
         | (GraphQL Mutation to trigger event)
         |
         v
+------------------+
| AppSync Resolver |
+--------+---------+
         | (Triggers Notification)
         |
         v
+------------------+        +--------------------------+
| AWS Pinpoint     |------->| Cloud Messaging Lambda   |
+------------------+        |      (SendNotif)         |
                          +-------------+------------+
                                        | (Get device_token)
                                        v
                             +--------------------------+
                             |         DynamoDB         |
                             +-------------+------------+
                                           | (Sends via Platform)
                                           v
+-------------------------------------------------+
| Apple Push Notifications / Firebase Cloud Msging|
+----------------------+--------------------------+
                       |
                       v
+-------------------------------------------------+
| User's Device (Receives Notification)           |
+-------------------------------------------------+

    

Workflow:

    Event Trigger: An action in the app (e.g., sending a friend request) triggers a GraphQL mutation via AppSync.

    Notification Logic: The Lambda resolver, after processing the request, makes a call to AWS Pinpoint.

    Targeting & Distribution: Pinpoint (or a Lambda it triggers) retrieves the target user's device_token from DynamoDB and sends the push notification payload to the appropriate platform service (APNS/FCM).

    Delivery: The user's device receives the push notification.

      
---
**Confirmation:** Once both files have been updated, the task is complete. The project documentation will now be fully aligned with the MVP architecture.