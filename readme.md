# umee - MVP

**A mobile application designed to help users meet new people through shared prompts, interests, and real-time interaction.**

This platform provides a scalable and secure solution for social discovery and communication. The architecture is built on AWS serverless and compute services to ensure real-time performance, security, and scalability.

## ✨ Core Features

- ** User Profiles:** Customizable profiles with personal bios and unique Q&A-style prompts.
- ** Meet Screen:** A discovery feature to find and connect with other users in your community.
- ** Real-Time Chat:** Low-latency, one-on-one messaging using a dedicated WebSocket layer.
- ** Newsfeed:** A community-based feed to see posts and updates.
- ** Push Notifications:** Instant updates for new messages, friend requests, and other important events.
- ** Secure by Design:** Authentication managed by AWS Cognito, with fine-grained access control using IAM.

## System Workflow Summary

This diagram provides a high-level overview of how the client application interacts with the various backend services to deliver the core features of the umee MVP.

```text
                               +-----------------------------+
                               |    Flutter Client (umee)    |
                               | (Profile, Meet, News, Chat) |
                               +--------------+--------------+
                                              |
      +---------------------------------------+------------------------------------------+
      | (GraphQL API & Auth)                  | (WebSocket)                              | (Push Notification)
      v                                       v                                          ^
+-----+-------------------+          +--------+---------------+                           |
| AWS Amplify SDK         |          | App Load Balancer (ALB)|                           |
+-----+-------------------+          +--------+---------------+                           |
      |                                       |                                          |
      v                                       v                                          |
+-----+-------------------+          +--------+---------------+          +----------------+-------+
| AWS AppSync (GraphQL)   |          | EC2 (Socket.io Server) |          | Cloud Messaging      |
+-----+-------------------+          +------------------------+          | (FCM / APNS)         |
      |                                                                  +--------+-------------+
      | (Invokes Resolvers)                                                       ^
      v                                                                           |
+-----+-------------------+                                              +--------+-------------+
| AWS Lambda Functions    |--------------------------------------------->|     AWS Pinpoint       |
+-----+-------------------+                                              +----------------------+
      |
      +---------------------+
      |                     |
      v                     v
+-----+------+       +------+------+
| DynamoDB   |       | Amazon S3   |
| (All Data) |       | (Images)    |
+------------+       +-------------+

    
Key Technologies

    Frontend: Flutter & Dart

    Backend Orchestration: AWS Amplify

    Authentication: AWS Cognito

    API: AWS AppSync (GraphQL)

    Business Logic: AWS Lambda

    Database: Amazon DynamoDB

    File Storage: Amazon S3

    Real-Time Chat: Socket.io on Amazon EC2

    Push Notifications: AWS Pinpoint

Getting Started
      
# Navigate to the frontend directory
cd frontend/

# Install dependencies
flutter pub get

# Run the application
flutter run

    
